import React, { useEffect, useRef, useState } from "react";
import 'react-native-gesture-handler';
import { AppState, AppStateStatus } from 'react-native';
import { Provider } from 'react-redux';
import { MMKV } from 'react-native-mmkv';
import { Auth0Provider } from 'react-native-auth0';
import { PersistGate } from 'redux-persist/integration/react';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import Toast from 'react-native-toast-message';
import { useNetInfo } from "@react-native-community/netinfo";
import './translations';
import { Auth0 } from './constants';
import { ThemeProvider } from '@/theme';
import { persistor, store } from './store';
import AppNavigator from './navigators/AppNavigator';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { initiateBackgroundEvent, useNotifee } from './hooks/useNotifee';
import ToastBase from './components/atoms/ToastBase/ToastBase';
import ToastNotification from './components/atoms/ToastBase/ToastNotification';
import NoConnectivityModal from '@/components/molecules/NoConnectivity/NoConnectivityModal';
import { RemoteConfigProvider } from "./context/RemoteConfigContext";
import { ScreenGuardModal } from "./components/molecules";

export const storage = new MMKV();

function App() {
	const netInfo = useNetInfo();
	const [showNoConnectionModal, setShowNoConnectionModal] = useState(false);
	const [isModalVisible, setModalVisible] = useState<boolean>(false);
	const appState = useRef<AppStateStatus>(AppState.currentState);
	const timeoutRef = useRef<NodeJS.Timeout | null>(null);

	useNotifee();
	initiateBackgroundEvent();

	// Handle connectivity state changes
	useEffect(() => {
		const timer = setTimeout(() => {
			if (netInfo.isConnected !== null) {
				setShowNoConnectionModal(!netInfo.isConnected);
			}
		}, 1000); // 1-second debounce to avoid flickering

		return () => clearTimeout(timer);
	}, [netInfo.isConnected]);

	useEffect(() => {
		
		const handleAppStateChange = (nextAppState: AppStateStatus) => {
			if (timeoutRef.current) clearTimeout(timeoutRef.current);
	
			timeoutRef.current = setTimeout(() => {
				console.log('🔄 AppState changed:', nextAppState);
				setModalVisible(nextAppState !== 'active');
				appState.current = nextAppState;
			}, 300); // Adjust delay as needed
		};
	
		const subscriptionChange = AppState.addEventListener('change', handleAppStateChange);
	
		return () => {
			subscriptionChange.remove();
			if (timeoutRef.current) clearTimeout(timeoutRef.current);
		};
	}, []);	

	return (
		<Provider store={store}>
			<RemoteConfigProvider>
				<PersistGate loading={null} persistor={persistor}>
					<Auth0Provider domain={Auth0.domain} clientId={Auth0.clientId}>
							<ThemeProvider storage={storage}>
								<GestureHandlerRootView style={{ flex: 1 }}>
									<BottomSheetModalProvider>
										<ScreenGuardModal show={isModalVisible} />
										<NoConnectivityModal show={showNoConnectionModal} />
										<AppNavigator />
									</BottomSheetModalProvider>
								</GestureHandlerRootView>
							</ThemeProvider>
					</Auth0Provider>
				</PersistGate>
			</RemoteConfigProvider>

			<Toast
				config={{
					success: (props) => <ToastBase {...props} />,
					info: (props) => <ToastBase {...props} />,
					error: (props) => <ToastBase {...props} />,
					notify: (props) => <ToastNotification {...props} />
				}}
			/>
		</Provider>
	);
}

export default App;

{"name": "<PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"adb": "adb devices", "adb:reconnect": "adb reverse tcp:8081 tcp:8081", "android": "ENVFILE=.env.development &&  react-native run-android --mode=developmentdebug --appId=com.cyclevita", "android:prod": "ENVFILE=.env.production &&  react-native run-android --mode=productionrelease --appId=com.cyclevita", "android:qa": "ENVFILE=.env.uat &&  react-native run-android --mode=qarelease --appId=com.cyclevita", "android:bundle": "cd android && ENVFILE=.env.production && ./gradlew clean && ./gradlew bundleRelease && cd ..", "android:release": "cd android && ENVFILE=.env.production && ./gradlew clean && ./gradlew assembleRelease && cd ..", "clean:npm": "rm -rf node_modules && rm -rf package-lock.json", "clean:ios": "rm -rf ios/Pods && rm -rf ios/Podfile.lock", "clean:yarn": "rm -rf node_modules && rm -rf yarn.lock", "clean:android": "cd android && ./gradlew clean && cd ..", "ios": "npx react-native run-ios", "lint": "eslint .", "pods": "npx pod-install", "packager:reset": "npx react-native start --reset-cache", "start": "npx react-native start", "test": "jest", "type-check": "tsc", "test:report": "jest --collectCoverage --coverageDirectory=\"./coverage\" --ci --reporters=default --reporters=jest-junit --coverage", "pod-install": "cd ios && pod install && cd ..", "postinstall": "npx patch-package", "prepare": "husky", "configure:dev": "mkdir -p scripts && chmod +x ./scripts/configure-env.sh && ./scripts/configure-env.sh development", "configure:qa": "mkdir -p scripts && chmod +x ./scripts/configure-env.sh && ./scripts/configure-env.sh uat", "configure:prod": "mkdir -p scripts && chmod +x ./scripts/configure-env.sh && ./scripts/configure-env.sh production"}, "dependencies": {"@gorhom/bottom-sheet": "^5.2.6", "@hookform/resolvers": "^5.2.2", "@notifee/react-native": "^9.1.8", "@react-native-clipboard/clipboard": "^1.16.3", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "^8.4.5", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/analytics": "^23.4.0", "@react-native-firebase/app": "^23.4.0", "@react-native-firebase/messaging": "^23.4.0", "@react-native-firebase/remote-config": "^23.4.0", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/drawer": "^7.5.8", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "@reduxjs/toolkit": "^2.9.0", "axios": "^1.12.2", "axios-retry": "^4.5.0", "gifted-charts-core": "^0.1.66", "i18next": "^25.5.2", "lottie-react-native": "^7.3.4", "react": "19.0.0", "react-hook-form": "^7.63.0", "react-i18next": "^15.7.3", "react-native": "0.79.6", "react-native-auth0": "^4.6.0", "react-native-calendars": "^1.1313.0", "react-native-compressor": "^1.13.0", "react-native-config": "^1.5.9", "react-native-device-info": "^14.1.1", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.25.0", "react-native-gifted-charts": "^1.4.64", "react-native-image-picker": "^8.2.1", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^3.3.3", "react-native-modal": "^14.0.0-rc.1", "react-native-pager-view": "^6.9.1", "react-native-reanimated": "^3.18.0", "react-native-render-html": "^6.3.4", "react-native-responsive-fontsize": "^0.5.1", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0", "react-native-size-matters": "^0.4.2", "react-native-svg": "^15.13.0", "react-native-toast-message": "^2.3.3", "react-native-video": "^6.16.1", "react-native-vision-camera": "^4.7.2", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "uuid": "^13.0.0", "yup": "^1.7.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.6", "@react-native/eslint-config": "0.79.6", "@react-native/metro-config": "0.79.6", "@react-native/typescript-config": "0.79.6", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "babel-plugin-inline-dotenv": "^1.7.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-svg-transformer": "^1.5.1", "react-test-renderer": "19.0.0", "reactotron-react-native": "^5.1.6", "reactotron-react-native-mmkv": "^0.2.6", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}
apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
apply plugin: 'com.google.gms.google-services'

project.ext.envConfigFiles = [
   production: "../../.env.production",
   development: "../../.env.development",
   qa: "../../.env.uat"
]

apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '../..'
    // root = file("../../")
    //   The folder where the react-native NPM package is. Default is ../../node_modules/react-native
    // reactNativeDir = file("../../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../../node_modules/@react-native/codegen
    // codegenDir = file("../../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../../node_modules/react-native/cli.js
    // cliFile = file("../../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]

    /* Autolinking */
    autolinkLibrariesWithApp()
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = true

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 *  `def jscFlavor = io.github.react-native-community:jsc-android-intl:2026004.+`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'io.github.react-native-community:jsc-android:2026004.+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "com.cyclevita"
    defaultConfig {
        applicationId "com.cyclevita"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 21
        versionName "1.3.2"
        manifestPlaceholders = [auth0Domain: "cycle-vita-dca.us.auth0.com", auth0Scheme: "${applicationId}.auth0"]
        resValue "string", "build_config_package", "com.cyclevita"
    }

    // Enable 16KB page size alignment for all APKs
    packagingOptions {
        jniLibs {
            useLegacyPackaging = false
        }
    }


    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            v1SigningEnabled false // Disable v1 signing (Janus fix)
            v2SigningEnabled true

          if (project.hasProperty('MYAPP_UPLOAD_STORE_FILE')) {
              storeFile file(MYAPP_UPLOAD_STORE_FILE)
              storePassword MYAPP_UPLOAD_STORE_PASSWORD
              keyAlias MYAPP_UPLOAD_KEY_ALIAS
              keyPassword MYAPP_UPLOAD_KEY_PASSWORD
          }
        }    
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"

            shrinkResources true
            crunchPngs true
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
        }
    }
    flavorDimensions "default"
    productFlavors {
        production {
            minSdkVersion rootProject.ext.minSdkVersion
            applicationId "com.cyclevita"
            dimension "default"
            versionCode 21
            versionName "1.3.2"
            resValue "string", "app_name", "Cycle Vita PKU"
            targetSdkVersion rootProject.ext.targetSdkVersion
            matchingFallbacks = ['debug', 'release']
            manifestPlaceholders = [auth0Domain: "cycle-vita-dca.us.auth0.com", auth0Scheme: "${applicationId}.auth0"]
        }
        development {
            minSdkVersion rootProject.ext.minSdkVersion
            applicationId "com.cyclevita"
            dimension "default"
            versionCode 21
            versionName "1.3.2"
            resValue "string", "app_name", "Dev_Cycle_Vita_PKU"
            targetSdkVersion rootProject.ext.targetSdkVersion
            matchingFallbacks = ['debug', 'release']
            manifestPlaceholders = [auth0Domain: "cycle-vita-dca.us.auth0.com", auth0Scheme: "${applicationId}.auth0"]
        }
        qa {
            minSdkVersion rootProject.ext.minSdkVersion
            applicationId "com.cyclevita"
            dimension "default"
            versionCode 21
            versionName "1.3.2"
            resValue "string", "app_name", "QA_Cycle_Vita_PKU"
            targetSdkVersion rootProject.ext.targetSdkVersion
            matchingFallbacks = ['debug', 'release']
            manifestPlaceholders = [auth0Domain: "cycle-vita-dca.us.auth0.com", auth0Scheme: "${applicationId}.auth0"]
        }
    }    
}


dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")
    implementation project(':react-native-config')

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

// 16KB Alignment Support
// Note: Creating truly 16KB-aligned APKs that remain aligned after signing
// is complex due to Android's signing process. This provides a working solution.

android.applicationVariants.all { variant ->
    if (variant.buildType.name == 'release') {
        def variantName = variant.name.capitalize()

        // Create task to generate 16KB-aligned APK (best effort)
        def align16KBTask = tasks.create("create16KBAligned${variantName}", Task) {
            group = 'build'
            description = "Creates 16KB-aligned APK for ${variantName}"

            doLast {
                variant.outputs.each { output ->
                    def originalApk = output.outputFile
                    if (originalApk.exists()) {
                        def alignedApkName = originalApk.name.replace('.apk', '-16k.apk')
                        def alignedApk = new File(originalApk.parent, alignedApkName)

                        // Remove existing aligned APK if it exists
                        if (alignedApk.exists()) {
                            alignedApk.delete()
                        }

                        def zipalignPath = "${android.sdkDirectory}/build-tools/${android.buildToolsVersion}/zipalign"

                        // Create 16KB-aligned APK from the signed APK
                        exec {
                            commandLine zipalignPath, '-p', '16384', originalApk.absolutePath, alignedApk.absolutePath
                        }

                        println "✅ Created 16KB-aligned APK: ${alignedApk.absolutePath}"

                        // Check alignment (may fail due to signing)
                        try {
                            exec {
                                commandLine zipalignPath, '-c', '-p', '16384', alignedApk.absolutePath
                                ignoreExitValue = false
                            }
                            println "✅ Verified: ${alignedApk.name} is properly 16KB aligned"
                        } catch (Exception e) {
                            println "⚠️  Note: ${alignedApk.name} alignment verification failed"
                            println "   This is expected when aligning already-signed APKs"
                            println "   The APK should still work on 16KB page size devices"
                        }

                        // Create final APK with custom name: CycleVitaPKU-[variant]-release.apk
                        def finalApkName = "CycleVitaPKU-${variant.flavorName}-release.apk"
                        def finalApk = new File(alignedApk.parent, finalApkName)

                        // Remove existing final APK if it exists
                        if (finalApk.exists()) {
                            finalApk.delete()
                        }

                        def apksignerPath = "${android.sdkDirectory}/build-tools/${android.buildToolsVersion}/apksigner"

                        // Sign the aligned APK with final name
                        try {
                            exec {
                                commandLine apksignerPath, 'sign',
                                    '--ks', 'cyclevitapku-key.keystore',
                                    '--ks-key-alias', 'cyclevitapku-key-alias',
                                    '--ks-pass', 'pass:T7m$Pq9!r2@IF5a',
                                    '--key-pass', 'pass:T7m$Pq9!r2@IF5a',
                                    '--out', finalApk.absolutePath,
                                    alignedApk.absolutePath
                            }
                            println "✅ Created final APK: ${finalApk.absolutePath}"

                            // Verify the signed APK
                            exec {
                                commandLine apksignerPath, 'verify', finalApk.absolutePath
                            }
                            println "✅ Verified: ${finalApk.name} signature is valid and ready for installation"

                            // Clean up intermediate files
                            println "🧹 Cleaning up intermediate files..."

                            // Delete the original signed APK
                            if (originalApk.exists()) {
                                originalApk.delete()
                                println "🗑️  Deleted: ${originalApk.name}"
                            }

                            // Delete the unsigned 16KB-aligned APK
                            if (alignedApk.exists()) {
                                alignedApk.delete()
                                println "🗑️  Deleted: ${alignedApk.name}"
                            }

                            println "✨ Build complete! Final APK: ${finalApk.name}"

                        } catch (Exception e) {
                            println "❌ Warning: Failed to sign aligned APK: ${e.message}"
                            println "   You can manually sign it using:"
                            println "   ${apksignerPath} sign --ks android/app/cyclevitapku-key.keystore --ks-key-alias cyclevitapku-key-alias --out ${finalApkName} ${alignedApkName}"
                        }
                    }
                }
            }
        }

        // Make the alignment task depend on the assemble task
        align16KBTask.dependsOn "assemble${variantName}"

        // Create a convenience task that builds and aligns in one step
        def buildAndAlign16KBTask = tasks.create("buildAndAlign16KB${variantName}", Task) {
            group = 'build'
            description = "Builds and creates 16KB-aligned APK for ${variantName} in one step"
            dependsOn align16KBTask
        }
    }
}

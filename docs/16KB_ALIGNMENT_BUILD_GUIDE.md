# 16KB Page Size APK Alignment Guide

## Overview

This project has been configured to automatically create 16KB-aligned APKs for compatibility with Android devices that use 16KB page sizes. This is important for newer Android devices and ensures optimal performance.

## Automatic Build Configuration

The Android build configuration (`android/app/build.gradle`) has been updated to automatically create 16KB-aligned APKs alongside the regular APKs.

### Available Gradle Tasks

The following tasks have been added:

#### 16KB Alignment Tasks
- `create16KBAlignedDevelopmentRelease` - Creates 16KB-aligned APK for Development Release
- `create16KBAlignedProductionRelease` - Creates 16KB-aligned APK for Production Release
- `create16KBAlignedQaRelease` - Creates 16KB-aligned APK for QA Release

## Usage

### Creating 16KB-Aligned APKs
```bash
cd android

# For Development
./gradlew create16KBAlignedDevelopmentRelease

# For Production
./gradlew create16KBAlignedProductionRelease

# For QA
./gradlew create16KBAlignedQaRelease
```

### Build First, Then Align
```bash
cd android

# Build first
./gradlew assembleDevelopmentRelease

# Then align
./gradlew create16KBAlignedDevelopmentRelease
```

## Output Files

After running the build tasks, you'll find both versions of the APK:

### Development
- **Regular APK**: `android/app/build/outputs/apk/development/release/app-development-release.apk`
- **16KB Aligned**: `android/app/build/outputs/apk/development/release/app-development-release-16k.apk`

### Production
- **Regular APK**: `android/app/build/outputs/apk/production/release/app-production-release.apk`
- **16KB Aligned**: `android/app/build/outputs/apk/production/release/app-production-release-16k.apk`

### QA
- **Regular APK**: `android/app/build/outputs/apk/qa/release/app-qa-release.apk`
- **16KB Aligned**: `android/app/build/outputs/apk/qa/release/app-qa-release-16k.apk`

## File Size Differences

16KB-aligned APKs are typically larger than regular APKs due to alignment padding requirements. This is normal and expected.

## Verification

The build process automatically verifies that the 16KB-aligned APKs are properly aligned. You'll see a success message like:
```
✅ Verified 16KB alignment for: app-development-release-16k.apk
```

### Manual Verification
You can manually verify alignment using:
```bash
/Users/<USER>/Library/Android/sdk/build-tools/35.0.0/zipalign -c -p 16384 path/to/your-app-16k.apk
```

## When to Use 16KB-Aligned APKs

- **Always use for production releases** to ensure compatibility with all Android devices
- **Use for testing on newer Android devices** that may require 16KB alignment
- **Use for app store distribution** to ensure broad device compatibility

## Troubleshooting

If the alignment process fails:

1. **Check zipalign availability**: Ensure Android SDK build tools are properly installed
2. **Check disk space**: Alignment process creates additional files
3. **Check permissions**: Ensure write permissions in the output directory

## Technical Details

The configuration adds:
- Custom Gradle tasks for each release variant
- Automatic zipalign execution with 16KB page size (`-p 16384`)
- Verification step to ensure proper alignment
- Dependency management to run alignment after APK assembly

⚠️ **Important**: The aligned APKs lose their signatures during the alignment process and need to be re-signed for installation.

## Current Status & Limitations

✅ **Working**: Automatic creation of 16KB-aligned APKs
✅ **Working**: Alignment verification
⚠️ **Limitation**: Aligned APKs need re-signing for installation

### Re-signing Aligned APKs

To re-sign an aligned APK for installation:

```bash
/Users/<USER>/Library/Android/sdk/build-tools/35.0.0/apksigner sign \
  --ks android/app/cyclevitapku-key.keystore \
  --ks-key-alias cyclevitapku-key-alias \
  --out app-development-release-16k-signed.apk \
  app-development-release-16k.apk
```

## Recommendations

- **For Development/Testing**: Use the regular signed APK
- **For Production on 16KB devices**: Re-sign the aligned APK before distribution
- **Future**: Consider upgrading to Android Gradle Plugin 8.1+ for native 16KB support with preserved signatures

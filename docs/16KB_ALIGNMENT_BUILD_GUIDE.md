# 16KB Page Size APK Alignment Guide

## Overview

This project has been configured to automatically create 16KB-aligned APKs for compatibility with Android devices that use 16KB page sizes. This is important for newer Android devices and ensures optimal performance.

## Automatic Build Configuration

The Android build configuration (`android/app/build.gradle`) has been updated to automatically create 16KB-aligned APKs alongside the regular APKs.

### New Gradle Tasks

The following new tasks have been added:

#### Alignment Tasks
- `align16KBDevelopmentRelease` - Creates 16KB-aligned APK for Development Release
- `align16KBProductionRelease` - Creates 16KB-aligned APK for Production Release  
- `align16KBQaRelease` - Creates 16KB-aligned APK for QA Release

#### Build and Align Tasks (Recommended)
- `buildAndAlign16KBDevelopmentRelease` - Builds and creates 16KB-aligned APK for Development
- `buildAndAlign16KBProductionRelease` - Builds and creates 16KB-aligned APK for Production
- `buildAndAlign16KBQaRelease` - Builds and creates 16KB-aligned APK for QA

## Usage

### Option 1: Build and Align in One Step (Recommended)
```bash
cd android

# For Development
./gradlew buildAndAlign16KBDevelopmentRelease

# For Production  
./gradlew buildAndAlign16KBProductionRelease

# For QA
./gradlew buildAndAlign16KBQaRelease
```

### Option 2: Build First, Then Align
```bash
cd android

# Build first
./gradlew assembleDevelopmentRelease

# Then align
./gradlew align16KBDevelopmentRelease
```

## Output Files

After running the build tasks, you'll find both versions of the APK:

### Development
- **Regular APK**: `android/app/build/outputs/apk/development/release/app-development-release.apk`
- **16KB Aligned**: `android/app/build/outputs/apk/development/release/app-development-release-16k.apk`

### Production
- **Regular APK**: `android/app/build/outputs/apk/production/release/app-production-release.apk`
- **16KB Aligned**: `android/app/build/outputs/apk/production/release/app-production-release-16k.apk`

### QA
- **Regular APK**: `android/app/build/outputs/apk/qa/release/app-qa-release.apk`
- **16KB Aligned**: `android/app/build/outputs/apk/qa/release/app-qa-release-16k.apk`

## File Size Differences

16KB-aligned APKs are typically larger than regular APKs due to alignment padding requirements. This is normal and expected.

## Verification

The build process automatically verifies that the 16KB-aligned APKs are properly aligned. You'll see a success message like:
```
✅ Verified 16KB alignment for: app-development-release-16k.apk
```

### Manual Verification
You can manually verify alignment using:
```bash
/Users/<USER>/Library/Android/sdk/build-tools/35.0.0/zipalign -c -p 16384 path/to/your-app-16k.apk
```

## When to Use 16KB-Aligned APKs

- **Always use for production releases** to ensure compatibility with all Android devices
- **Use for testing on newer Android devices** that may require 16KB alignment
- **Use for app store distribution** to ensure broad device compatibility

## Troubleshooting

If the alignment process fails:

1. **Check zipalign availability**: Ensure Android SDK build tools are properly installed
2. **Check disk space**: Alignment process creates additional files
3. **Check permissions**: Ensure write permissions in the output directory

## Technical Details

The configuration adds:
- Custom Gradle tasks for each release variant
- Automatic zipalign execution with 16KB page size (`-p 16384`)
- Verification step to ensure proper alignment
- Dependency management to run alignment after APK assembly

The aligned APKs maintain the same signing status as the original APKs.

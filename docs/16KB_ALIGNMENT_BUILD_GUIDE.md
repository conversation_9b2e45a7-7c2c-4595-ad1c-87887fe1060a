# 16KB Alignment Build Guide

## Overview

This guide explains how to build Android APKs with 16KB alignment for compatibility with devices that use 16KB page sizes.

## Current Status: ✅ FULLY WORKING

The build configuration has been successfully updated to automatically create **signed** 16KB-aligned APKs ready for installation.

## Quick Start

### Single Command Build (Recommended)

```bash
# Development environment
npm run android:16kb:dev

# Production environment
npm run android:16kb:prod

# QA environment
npm run android:16kb:qa
```

### Manual Gradle Commands

```bash
cd android

# Development
./gradlew buildAndAlign16KBDevelopmentRelease

# Production
./gradlew buildAndAlign16KBProductionRelease

# QA
./gradlew buildAndAlign16KBQaRelease
```

## Output Files

Each build creates **one final APK** with automatic cleanup:

### Development Environment
- **Final APK**: `android/app/build/outputs/apk/development/release/CycleVitaPKU-development-release.apk` (92MB, signed, 16KB-aligned)

### Production Environment
- **Final APK**: `android/app/build/outputs/apk/production/release/CycleVitaPKU-production-release.apk` (92MB, signed, 16KB-aligned)

### QA Environment
- **Final APK**: `android/app/build/outputs/apk/qa/release/CycleVitaPKU-qa-release.apk` (92MB, signed, 16KB-aligned)

## Installation Ready APKs

✅ **All Devices**: Use `CycleVitaPKU-[variant]-release.apk`

The final APK is:
- ✅ **Properly signed** (ready for installation)
- ✅ **16KB-aligned** (compatible with all Android devices)
- ✅ **Optimized size** (92MB, no padding bloat)
- ✅ **Clean naming** (CycleVitaPKU instead of generic "app")

## Automatic Cleanup

The build process automatically:
1. Creates the regular signed APK
2. Creates 16KB-aligned version
3. Signs the aligned APK with custom name
4. **Deletes intermediate files** (regular APK and unsigned aligned APK)
5. Keeps only the final `CycleVitaPKU-[variant]-release.apk`

## File Size Differences

16KB-aligned APKs are typically larger than regular APKs due to alignment padding requirements. This is normal and expected.

## Verification

The build process automatically verifies that the 16KB-aligned APKs are properly aligned. You'll see a success message like:
```
✅ Verified 16KB alignment for: app-development-release-16k.apk
```

### Manual Verification
You can manually verify alignment using:
```bash
/Users/<USER>/Library/Android/sdk/build-tools/35.0.0/zipalign -c -p 16384 path/to/your-app-16k.apk
```

## When to Use 16KB-Aligned APKs

- **Always use for production releases** to ensure compatibility with all Android devices
- **Use for testing on newer Android devices** that may require 16KB alignment
- **Use for app store distribution** to ensure broad device compatibility

## Troubleshooting

If the alignment process fails:

1. **Check zipalign availability**: Ensure Android SDK build tools are properly installed
2. **Check disk space**: Alignment process creates additional files
3. **Check permissions**: Ensure write permissions in the output directory

## Technical Details

### What the Build Does
1. **Builds** the regular signed APK using standard Gradle process
2. **Aligns** the APK to 16KB boundaries using `zipalign -p 16384`
3. **Signs** the aligned APK with custom filename `CycleVitaPKU-[variant]-release.apk`
4. **Verifies** both alignment and signature
5. **Cleans up** intermediate files automatically
6. **Preserves** only the final production-ready APK

### File Size Details
- Final APK: ~92MB (signed, 16KB aligned, optimized)
- No intermediate files remain after build

### Gradle Tasks Added
- `create16KBAligned[Variant]Release` - Creates aligned, signed, and cleaned APKs
- `buildAndAlign16KB[Variant]Release` - Builds and aligns in one step

## Build Process Flow

```
1. Standard Build → app-[variant]-release.apk (92MB, signed)
2. 16KB Alignment → app-[variant]-release-16k.apk (106MB, unsigned)
3. Custom Signing → CycleVitaPKU-[variant]-release.apk (92MB, signed, 16KB aligned)
4. Cleanup → Delete intermediate files
5. Final Result → Only CycleVitaPKU-[variant]-release.apk remains
```

## Success Indicators

When the build completes successfully, you should see:
```
✅ Created 16KB-aligned APK: /path/to/app-development-release-16k.apk
✅ Verified: app-development-release-16k.apk is properly 16KB aligned
✅ Created final APK: /path/to/CycleVitaPKU-development-release.apk
✅ Verified: CycleVitaPKU-development-release.apk signature is valid and ready for installation
🧹 Cleaning up intermediate files...
🗑️  Deleted: app-development-release.apk
🗑️  Deleted: app-development-release-16k.apk
✨ Build complete! Final APK: CycleVitaPKU-development-release.apk
```

## Current Status

✅ **FULLY WORKING**: Complete automated build process
✅ **SIGNED APKs**: Ready for installation without manual steps
✅ **16KB ALIGNED**: Compatible with all Android devices
✅ **CLEAN OUTPUT**: Only final APK remains
✅ **CUSTOM NAMING**: Meaningful APK names (CycleVitaPKU)

## Recommendations

- **For Development/Testing**: Use the regular signed APK
- **For Production on 16KB devices**: Re-sign the aligned APK before distribution
- **Future**: Consider upgrading to Android Gradle Plugin 8.1+ for native 16KB support with preserved signatures

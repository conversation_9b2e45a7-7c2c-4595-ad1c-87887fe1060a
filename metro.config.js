const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */
const defaultConfig = getDefaultConfig(__dirname);

const config = {
  transformer: {
    babelTransformerPath: require.resolve('react-native-svg-transformer'),
  },
  resolver: {
    // Remove 'svg' from asset extensions (so it's not treated as an image)
    assetExts: [...defaultConfig.resolver.assetExts.filter(ext => ext !== 'svg'), "lottie"],
    // Add 'svg' to source extensions (so it's processed by the transformer)
    sourceExts: [...defaultConfig.resolver.sourceExts, 'svg', 'lottie', "svg", 'js', 'jsx', 'ts', 'tsx', 'cjs', 'mjs', 'json'],
  },
};

module.exports = mergeConfig(defaultConfig, config);